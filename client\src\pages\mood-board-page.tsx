import { withAuth } from "@/lib/with-auth"
import DashboardLayoutWrapper from "@/components/layout/dashboard-layout"
import MoodBoardList from "@/components/tools/mood-board-list"

function MoodBoardPageContent() {
  return <MoodBoardList />
}

function MoodBoardPage() {
  return (
    <DashboardLayoutWrapper pageTitle="Mood Boards">
      <MoodBoardPageContent />
    </DashboardLayoutWrapper>
  )
}

// Enable authentication for moodboard functionality
export default withAuth(MoodBoardPage)
