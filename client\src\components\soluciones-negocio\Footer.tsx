import React from "react";
import { motion } from "framer-motion";
import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin } from "lucide-react";
import { Link } from "wouter";

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white pt-20 pb-10 px-4 sm:px-6 lg:px-8">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-16">
          <div>
            <div className="flex items-center mb-6">
              <div className="bg-purple-600 w-10 h-10 rounded-full flex items-center justify-center text-white mr-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-5 h-5"
                >
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                </svg>
              </div>
              <span className="text-xl font-bold">EmmaAI</span>
            </div>
            <p className="text-gray-400 mb-6">
              Transformando el marketing con agentes IA autónomos que ejecutan, optimizan y escalan tus campañas.
            </p>
            <div className="flex space-x-4">
              <motion.a
                href="#"
                className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-purple-600 hover:text-white transition-colors"
                whileHover={{ y: -3 }}
              >
                <Facebook size={18} />
              </motion.a>
              <motion.a
                href="#"
                className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-purple-600 hover:text-white transition-colors"
                whileHover={{ y: -3 }}
              >
                <Twitter size={18} />
              </motion.a>
              <motion.a
                href="#"
                className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-purple-600 hover:text-white transition-colors"
                whileHover={{ y: -3 }}
              >
                <Instagram size={18} />
              </motion.a>
              <motion.a
                href="#"
                className="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center text-gray-400 hover:bg-purple-600 hover:text-white transition-colors"
                whileHover={{ y: -3 }}
              >
                <Linkedin size={18} />
              </motion.a>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-6 border-b border-gray-700 pb-2">Soluciones</h3>
            <ul className="space-y-3">
              <li>
                <a href="/marketing-automation" className="text-gray-400 hover:text-white transition-colors">Marketing Automation</a>
              </li>
              <li>
                <a href="/content-creation" className="text-gray-400 hover:text-white transition-colors">Content Creation</a>
              </li>
              <li>
                <a href="/data-analytics" className="text-gray-400 hover:text-white transition-colors">Data Analytics</a>
              </li>
              <li>
                <a href="/social-media" className="text-gray-400 hover:text-white transition-colors">Social Media</a>
              </li>
              <li>
                <a href="/email-marketing" className="text-gray-400 hover:text-white transition-colors">Email Marketing</a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-6 border-b border-gray-700 pb-2">Empresa</h3>
            <ul className="space-y-3">
              <li>
                <a href="/about" className="text-gray-400 hover:text-white transition-colors">Sobre Nosotros</a>
              </li>
              <li>
                <a href="/careers" className="text-gray-400 hover:text-white transition-colors">Carreras</a>
              </li>
              <li>
                <a href="/blog" className="text-gray-400 hover:text-white transition-colors">Blog</a>
              </li>
              <li>
                <a href="/press" className="text-gray-400 hover:text-white transition-colors">Prensa</a>
              </li>
              <li>
                <a href="/partners" className="text-gray-400 hover:text-white transition-colors">Partners</a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-6 border-b border-gray-700 pb-2">Contacto</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <Mail className="w-5 h-5 text-purple-400 mr-3 mt-1 flex-shrink-0" />
                <span className="text-gray-400"><EMAIL></span>
              </li>
              <li className="flex items-start">
                <Phone className="w-5 h-5 text-purple-400 mr-3 mt-1 flex-shrink-0" />
                <span className="text-gray-400">+****************</span>
              </li>
              <li className="flex items-start">
                <MapPin className="w-5 h-5 text-purple-400 mr-3 mt-1 flex-shrink-0" />
                <span className="text-gray-400">
                  123 Innovation Drive<br />
                  San Francisco, CA 94107
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-500 text-sm mb-4 md:mb-0">
            &copy; {new Date().getFullYear()} EmmaAI. Todos los derechos reservados.
          </p>
          <div className="flex space-x-6">
            <a href="/privacy" className="text-gray-500 hover:text-white text-sm transition-colors">
              Política de Privacidad
            </a>
            <a href="/terms" className="text-gray-500 hover:text-white text-sm transition-colors">
              Términos de Servicio
            </a>
            <a href="/cookies" className="text-gray-500 hover:text-white text-sm transition-colors">
              Política de Cookies
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
