import React from "react";
import { motion } from "framer-motion";
import { <PERSON>R<PERSON>, <PERSON> } from "lucide-react";
import { <PERSON> } from "wouter";

const CTASection: React.FC = () => {
  return (
    <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <div className="bg-gradient-to-br from-purple-600 to-indigo-600 rounded-3xl overflow-hidden shadow-2xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="p-8 md:p-12 lg:p-16">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <h2 className="text-3xl md:text-4xl font-black mb-6 text-white leading-tight">
                  Transforma tu Marketing con Agentes IA Autónomos
                </h2>
                <p className="text-xl text-white/90 mb-8 max-w-lg">
                  Únete a las empresas líderes que están revolucionando su estrategia de marketing con Emma IA. Comienza hoy y experimenta resultados inmediatos.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/demo">
                    <motion.button
                      className="bg-white text-purple-600 font-black py-4 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-gray-50 transition-all duration-300"
                      whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
                      whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
                    >
                      <span className="flex items-center justify-center gap-2">
                        Comenzar Ahora <Rocket size={20} />
                      </span>
                    </motion.button>
                  </Link>
                  <Link href="/contacto">
                    <motion.button
                      className="bg-transparent text-white font-bold py-4 px-8 rounded-xl border-4 border-white hover:bg-white/10 transition-all duration-300"
                      whileHover={{ y: -5 }}
                      whileTap={{ y: 0 }}
                    >
                      <span className="flex items-center justify-center gap-2">
                        Hablar con Ventas <ArrowRight size={20} />
                      </span>
                    </motion.button>
                  </Link>
                </div>
              </motion.div>
            </div>
            
            <div className="relative hidden lg:block">
              <div className="absolute inset-0 bg-black/20"></div>
              <img 
                src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" 
                alt="Equipo de marketing usando Emma IA" 
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>

        {/* La sección de Planes y Precios ha sido eliminada */}
      </div>
    </section>
  );
};

export default CTASection;
