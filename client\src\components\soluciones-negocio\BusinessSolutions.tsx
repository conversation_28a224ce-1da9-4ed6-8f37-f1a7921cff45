import React from "react";
import { motion } from "framer-motion";
import { businessSolutions } from "./data";
import { ArrowRight } from "lucide-react";
import { <PERSON> } from "wouter";

const BusinessSolutions: React.FC = () => {
  return (
    <section id="soluciones" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-black mb-4 text-gray-900">
              Soluciones de <span className="text-purple-600">Marketing IA</span> para tu Negocio
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Nuestros agentes IA están diseñados para transformar cada aspecto de tu estrategia de marketing, 
              desde la creación de contenido hasta el análisis de datos.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {businessSolutions.map((solution, index) => (
            <motion.div
              key={index}
              className="rounded-2xl overflow-hidden shadow-xl border-2 border-gray-100 hover:border-purple-200 transition-all duration-300"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{ y: -10 }}
            >
              <div className={`p-6 ${solution.bgColor}`}>
                <div className="w-14 h-14 rounded-xl flex items-center justify-center border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] mb-4">
                  {solution.icon}
                </div>
                <h3 className="text-2xl font-bold text-white mb-2">{solution.title}</h3>
                <p className="text-white/90">{solution.description}</p>
              </div>
              <div className="p-6 bg-white">
                <ul className="space-y-3 mb-6">
                  {solution.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start">
                      <svg
                        className="w-5 h-5 text-green-500 mr-2 mt-1 flex-shrink-0"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M5 13l4 4L19 7"
                        ></path>
                      </svg>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Link href={`/solutions/${solution.title.toLowerCase().replace(/\s+/g, '-')}`}>
                  <motion.button
                    className="w-full bg-gray-100 hover:bg-gray-200 text-gray-900 font-bold py-3 px-4 rounded-xl flex items-center justify-center gap-2 transition-colors"
                    whileHover={{ y: -3 }}
                    whileTap={{ y: 0 }}
                  >
                    Conocer Más <ArrowRight size={18} />
                  </motion.button>
                </Link>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <Link href="/all-solutions">
            <motion.button
              className="bg-white text-purple-600 font-black py-3 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-gray-50 transition-all duration-300"
              whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
              whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
            >
              <span className="flex items-center justify-center gap-2">
                Ver Todas las Soluciones <ArrowRight size={18} />
              </span>
            </motion.button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default BusinessSolutions;
