import React from "react";
import { motion } from "framer-motion";
import { agentCapabilities } from "./data";


const AgentCapabilities: React.FC = () => {
  return (
    <section id="capacidades" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-black mb-4 text-gray-900">
              Capacidades de <span className="text-purple-600">Nuestros Agentes IA</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Descubre todo lo que nuestros agentes IA pueden hacer para transformar tu estrategia de marketing
              y llevar tu negocio al siguiente nivel.
            </p>
          </motion.div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {agentCapabilities.map((capability, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-2xl shadow-lg border-2 border-gray-100 hover:border-purple-200 transition-all duration-300 overflow-hidden"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.05 }}
              whileHover={{ y: -10 }}
            >
              <div className={`${capability.color} p-4 flex items-center justify-center`}>
                <div className="w-14 h-14 rounded-xl flex items-center justify-center border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]">
                  {capability.icon}
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">{capability.title}</h3>
                <p className="text-gray-700">{capability.description}</p>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="mt-16 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-2xl p-8 text-white shadow-xl"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl font-bold mb-4">Personalización Avanzada</h3>
              <p className="text-white/90 mb-6">
                Todos nuestros agentes IA pueden ser personalizados para adaptarse perfectamente a tu industria, 
                marca y objetivos específicos. Aprenden continuamente de tus datos y resultados para mejorar 
                constantemente su rendimiento.
              </p>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <svg
                    className="w-5 h-5 text-green-300 mr-2 mt-1 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    ></path>
                  </svg>
                  <span>Adaptación a tu tono y voz de marca</span>
                </li>
                <li className="flex items-start">
                  <svg
                    className="w-5 h-5 text-green-300 mr-2 mt-1 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    ></path>
                  </svg>
                  <span>Entrenamiento con tus datos históricos</span>
                </li>
                <li className="flex items-start">
                  <svg
                    className="w-5 h-5 text-green-300 mr-2 mt-1 flex-shrink-0"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M5 13l4 4L19 7"
                    ></path>
                  </svg>
                  <span>Optimización continua basada en resultados</span>
                </li>
              </ul>
            </div>
            <div className="block">
              <div className="relative p-8">
                <img
                  src="/ai-assistant.svg"
                  alt="AI Assistant"
                  className="w-full h-auto object-cover"
                />
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AgentCapabilities;
