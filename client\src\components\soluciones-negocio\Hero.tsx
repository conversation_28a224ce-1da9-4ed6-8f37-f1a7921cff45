import React from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON> } from "lucide-react";
import { <PERSON> } from "wouter";

const Hero: React.FC = () => {
  return (
    <section className="pt-40 pb-24 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 z-0"></div>
      
      {/* Elementos decorativos con estilo neo-brutalism */}
      <motion.div 
        className="absolute top-40 right-[10%] w-20 h-20 rounded-full bg-yellow-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
        animate={{ 
          y: [0, -20, 0],
          rotate: [0, 10, 0]
        }}
        transition={{ 
          duration: 5,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      />
      
      <motion.div 
        className="absolute bottom-20 left-[10%] w-16 h-16 rounded-full bg-blue-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
        animate={{ 
          y: [0, 15, 0],
          rotate: [0, -8, 0]
        }}
        transition={{ 
          duration: 4,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 1
        }}
      />

      <motion.div 
        className="absolute top-60 left-[15%] w-12 h-12 rounded-full bg-pink-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
        animate={{ 
          y: [0, 10, 0],
          x: [0, 10, 0]
        }}
        transition={{ 
          duration: 6,
          repeat: Infinity,
          repeatType: "reverse",
          delay: 2
        }}
      />
      
      <div className="container mx-auto relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="max-w-2xl"
          >
            <motion.div 
              className="inline-block bg-blue-300 text-blue-900 font-bold text-sm px-4 py-2 rounded-full mb-6 border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]"
              whileHover={{ scale: 1.05 }}
            >
              Soluciones de Marketing Impulsadas por IA
            </motion.div>
            <h1 className="text-5xl sm:text-6xl font-black mb-6 leading-tight text-gray-900">
              Revoluciona tu <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">Estrategia de Marketing</span> con Agentes IA
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Reduce hasta 70% tu gasto en marketing y publica 3 veces más contenido con Emma: profesionales digitales impulsados por IA que escriben, diseñan, lanzan y automatizan todo tu marketing sin freelancers, sin agencias y sin perder tiempo.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/demo">
                <motion.button
                  className="bg-blue-500 text-white font-black py-4 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-blue-600 transition-all duration-300"
                  whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
                  whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
                >
                  <span className="flex items-center justify-center gap-2">
                    Comenzar Ahora <Rocket size={20} />
                  </span>
                </motion.button>
              </Link>
              <Link href="/demo">
                <motion.button
                  className="bg-white text-blue-600 font-black py-4 px-8 rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-gray-50 transition-all duration-300"
                  whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
                  whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
                >
                  <span className="flex items-center justify-center gap-2">
                    Ver Demo <Play size={20} />
                  </span>
                </motion.button>
              </Link>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="relative">
              <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-1">
                <div className="bg-white rounded-xl p-6 border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)]">
                  <div className="bg-gray-900 rounded-lg p-2 flex items-center mb-6">
                    <div className="flex space-x-2 mr-auto">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="text-white/80 text-sm">Emma AI Marketing Assistant</div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-white font-bold">E</span>
                      </div>
                      <div className="bg-gray-100 rounded-xl rounded-tl-none p-4 text-gray-800">
                        <p>Hola, soy Emma, tu asistente de marketing IA. ¿En qué puedo ayudarte hoy?</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start justify-end">
                      <div className="bg-blue-600 rounded-xl rounded-tr-none p-4 text-white">
                        <p>Necesito crear una campaña de email marketing para nuestro nuevo producto.</p>
                      </div>
                      <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center ml-3 flex-shrink-0">
                        <span className="text-white font-bold">U</span>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-white font-bold">E</span>
                      </div>
                      <div className="bg-gray-100 rounded-xl rounded-tl-none p-4 text-gray-800">
                        <p>Perfecto. Crearé una campaña completa de email marketing para tu nuevo producto.</p>
                        <div className="mt-3 space-y-2">
                          <div className="bg-gray-200 rounded-lg p-2 text-sm">
                            <p className="font-bold text-blue-600">Tareas en progreso:</p>
                            <ul className="mt-1 space-y-1">
                              <li className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-green-400 mr-2"></div>
                                <span>Analizando datos de audiencia...</span>
                              </li>
                              <li className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-yellow-400 mr-2"></div>
                                <span>Generando asuntos de email...</span>
                              </li>
                              <li className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-blue-400 mr-2"></div>
                                <span>Diseñando secuencia de nurturing...</span>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-3 flex-shrink-0">
                        <span className="text-white font-bold">E</span>
                      </div>
                      <div className="bg-gray-100 rounded-xl rounded-tl-none p-4 text-gray-800">
                        <p className="font-bold text-green-600">¡Campaña creada con éxito!</p>
                        <p className="mt-2">He creado una campaña de 5 emails con segmentación por comportamiento de usuario.</p>
                        <div className="mt-3 flex space-x-2">
                          <button className="bg-blue-600 text-white text-sm py-1 px-3 rounded-lg border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">Ver campaña</button>
                          <button className="bg-gray-700 text-white text-sm py-1 px-3 rounded-lg border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">Editar</button>
                          <button className="bg-gray-700 text-white text-sm py-1 px-3 rounded-lg border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.9)]">Programar</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Decorative elements */}
            <motion.div 
              className="absolute -top-6 -right-6 w-20 h-20 bg-yellow-400 rounded-full opacity-80 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, 0]
              }}
              transition={{ 
                duration: 5,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            />
            <motion.div 
              className="absolute -bottom-6 -left-6 w-20 h-20 bg-pink-400 rounded-full opacity-80 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)]"
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, -5, 0]
              }}
              transition={{ 
                duration: 5,
                repeat: Infinity,
                repeatType: "reverse",
                delay: 0.5
              }}
            />
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
