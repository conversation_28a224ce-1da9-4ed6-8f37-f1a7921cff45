/**
 * Product Image Upload Component
 * Handles file upload with validation and preview
 */

import { useRef } from "react";
import { Check } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { validateImageFile } from "@/lib/utils/file-validation";

interface ProductImageUploadProps {
  uploadedImage: File | null;
  onImageUpload: (file: File | null) => void;
}

export function ProductImageUpload({ uploadedImage, onImageUpload }: ProductImageUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast({
        title: "Archivo inválido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    onImageUpload(file);
  };

  return (
    <div className="bg-gradient-to-br from-white to-slate-50 backdrop-blur-xl p-6 rounded-2xl border border-slate-200/60 shadow-lg">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-lg flex items-center justify-center">
          <span className="text-white text-sm">📷</span>
        </div>
        <label className="text-lg font-bold text-slate-900">
          Imagen del producto
        </label>
      </div>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        className="hidden"
      />
      <button
        onClick={() => fileInputRef.current?.click()}
        className="w-full p-4 border-2 border-dashed border-slate-300 rounded-xl text-slate-600 hover:border-[#3018ef] hover:text-[#3018ef] hover:bg-[#3018ef]/5 transition-all duration-300 flex items-center justify-center gap-3 font-medium"
      >
        <div className="w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-lg flex items-center justify-center">
          <span className="text-white text-sm">📷</span>
        </div>
        {uploadedImage ? "Cambiar imagen del producto" : "Subir imagen del producto"}
      </button>
      {uploadedImage && (
        <div className="mt-3 text-center">
          <p className="text-sm text-green-600 flex items-center justify-center gap-2">
            <Check className="w-4 h-4" />
            Imagen subida exitosamente
          </p>
        </div>
      )}
    </div>
  );
}
