/**
 * Componente principal para transferencia de estilo
 * Permite subir dos imágenes y transferir el estilo de una a la otra
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useToast } from '@/hooks/use-toast';
// import { useSavedStyleTransfers } from '@/hooks/use-saved-style-transfers';
import {
  transferStyleWithProgress,
  StyleTransferOptions,
} from '@/services/style-transfer-service';
import { useBackgroundTasks } from '@/context/BackgroundTasksContext';
import {
  Upload,
  Image as ImageIcon,
  Wand2,
  Loader2,
  Download,
  Heart,
  Copy,
  Palette,
  Sparkles,
  Trash2,
  ArrowRight,
} from 'lucide-react';

// Hook simplificado para localStorage compatible con Chrome y Safari
const useLocalStorage = <T,>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    // Verificación más robusta para SSR
    if (typeof window === "undefined" || typeof localStorage === "undefined") {
      return initialValue;
    }

    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);

      // Verificación adicional para localStorage
      if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
        localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  return [storedValue, setValue];
};

export default function StyleTransferGenerator() {
  // Debug: Verificar que el componente se está cargando
  console.log('StyleTransferGenerator: Component loading...');

  const [initImage, setInitImage] = useState<File | null>(null);
  const [initImagePreview, setInitImagePreview] = useState<string | null>(null);
  const [styleImage, setStyleImage] = useState<File | null>(null);
  const [styleImagePreview, setStyleImagePreview] = useState<string | null>(null);
  const [prompt, setPrompt] = useState('');
  const [negativePrompt, setNegativePrompt] = useState('');
  const [styleStrength, setStyleStrength] = useState([1.0]);
  const [compositionFidelity, setCompositionFidelity] = useState([0.9]);
  const [changeStrength, setChangeStrength] = useState([0.9]);
  const [seed, setSeed] = useState(0);
  const [outputFormat, setOutputFormat] = useState('png');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [currentImageSaved, setCurrentImageSaved] = useState(false);

  // Estados para la funcionalidad de guardados
  const [savedStyleTransfers, setSavedStyleTransfers] = useLocalStorage<any[]>('emma_saved_style_transfers', []);
  const [activeTab, setActiveTab] = useState("latest");

  // Debug: Verificar localStorage
  useEffect(() => {
    console.log('StyleTransferGenerator: localStorage check', {
      hasLocalStorage: typeof localStorage !== 'undefined',
      savedCount: savedStyleTransfers.length
    });
  }, [savedStyleTransfers]);

  const initImageInputRef = useRef<HTMLInputElement>(null);
  const styleImageInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { addTask, updateTask } = useBackgroundTasks();

  // Verificar si la imagen actual está guardada
  useEffect(() => {
    if (generatedImage) {
      const isImageSaved = savedStyleTransfers.some(transfer => transfer.processedUrl === generatedImage);
      setCurrentImageSaved(isImageSaved);
    }
  }, [generatedImage, savedStyleTransfers]);

  // Manejar selección de imagen original
  const handleInitImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setInitImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setInitImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Manejar selección de imagen de estilo
  const handleStyleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setStyleImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setStyleImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Generar semilla aleatoria
  const generateRandomSeed = () => {
    setSeed(Math.floor(Math.random() * 4294967294));
  };

  // Manejar generación de imagen
  const handleGenerate = async () => {
    if (!initImage || !styleImage) {
      toast({
        title: "❌ Error",
        description: "Debes subir tanto la imagen original como la imagen de estilo.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setGeneratedImage(null);

    try {
      const taskId = `style_transfer_${Date.now()}`;

      const options: StyleTransferOptions = {
        initImage,
        styleImage,
        prompt: prompt.trim(),
        negativePrompt: negativePrompt.trim() || undefined,
        styleStrength: styleStrength[0],
        compositionFidelity: compositionFidelity[0],
        changeStrength: changeStrength[0],
        seed: seed === 0 ? undefined : seed,
        outputFormat: outputFormat as "jpeg" | "png" | "webp",
      };

      const imageUrl = await transferStyleWithProgress(
        options,
        updateTask,
        taskId,
        true // keepInOriginPage
      );

      setGeneratedImage(imageUrl);

      toast({
        title: "✅ ¡Éxito!",
        description: "Estilo transferido exitosamente.",
      });

    } catch (error) {
      console.error('Error generating style transfer:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error desconocido al transferir estilo.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!generatedImage || !initImagePreview || !styleImagePreview) return;

    try {
      if (currentImageSaved) {
        // Quitar de favoritos
        const savedTransfer = savedStyleTransfers.find(transfer => transfer.processedUrl === generatedImage);
        if (savedTransfer) {
          const filteredTransfers = savedStyleTransfers.filter(transfer => transfer.id !== savedTransfer.id);
          setSavedStyleTransfers(filteredTransfers);
          setCurrentImageSaved(false);

          toast({
            title: "💔 Eliminada de favoritos",
            description: "La imagen ha sido eliminada de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const transferData = {
          id: `style_transfer_${Date.now()}_${Math.floor(Math.random() * 1000000)}`,
          initImageUrl: initImagePreview,
          styleImageUrl: styleImagePreview,
          processedUrl: generatedImage,
          initImageFilename: initImage?.name || "imagen-original",
          styleImageFilename: styleImage?.name || "imagen-estilo",
          prompt: prompt,
          negativePrompt: negativePrompt || undefined,
          styleStrength: styleStrength[0],
          compositionFidelity: compositionFidelity[0],
          changeStrength: changeStrength[0],
          seed: seed === 0 ? undefined : seed,
          outputFormat: outputFormat,
          timestamp: Date.now(),
          isFavorite: true,
        };

        const updatedTransfers = [transferData, ...savedStyleTransfers].slice(0, 50); // Limitar a 50
        setSavedStyleTransfers(updatedTransfers);
        setCurrentImageSaved(true);

        toast({
          title: "❤️ ¡Guardada en favoritos!",
          description: "Imagen guardada exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo guardar la imagen. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [generatedImage, initImagePreview, styleImagePreview, initImage?.name, styleImage?.name, prompt, negativePrompt, styleStrength, compositionFidelity, changeStrength, seed, outputFormat, currentImageSaved, savedStyleTransfers, setSavedStyleTransfers, toast]);

  // Copiar imagen al portapapeles con mejor compatibilidad
  const copyToClipboard = async () => {
    if (!generatedImage) return;

    try {
      // Verificar si el navegador soporta clipboard API
      if (!navigator.clipboard || !navigator.clipboard.write) {
        throw new Error('Clipboard API not supported');
      }

      const response = await fetch(generatedImage);
      const blob = await response.blob();

      // Verificar que el blob sea válido
      if (!blob || blob.size === 0) {
        throw new Error('Invalid image data');
      }

      await navigator.clipboard.write([
        new ClipboardItem({ [blob.type]: blob })
      ]);

      toast({
        title: "📋 ¡Copiado!",
        description: "Imagen copiada al portapapeles.",
      });
    } catch (error) {
      console.error('Error copying to clipboard:', error);

      // Fallback: intentar copiar como texto (data URL)
      try {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          await navigator.clipboard.writeText(generatedImage);
          toast({
            title: "📋 ¡Copiado!",
            description: "URL de imagen copiada al portapapeles.",
          });
        } else {
          throw new Error('No clipboard support');
        }
      } catch (fallbackError) {
        toast({
          title: "❌ Error",
          description: "Tu navegador no soporta copiar imágenes. Usa el botón de descargar.",
          variant: "destructive",
        });
      }
    }
  };

  // Descargar imagen con mejor compatibilidad
  const downloadImage = () => {
    if (!generatedImage) return;

    try {
      const link = document.createElement('a');
      link.href = generatedImage;
      link.download = `style-transfer-${Date.now()}.${outputFormat}`;

      // Asegurar que el link sea válido
      link.style.display = 'none';
      document.body.appendChild(link);

      // Trigger download
      link.click();

      // Cleanup
      setTimeout(() => {
        document.body.removeChild(link);
      }, 100);

      toast({
        title: "💾 ¡Descargado!",
        description: "Imagen descargada exitosamente.",
      });
    } catch (error) {
      console.error('Error downloading image:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo descargar la imagen.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="w-full">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="latest">Última Generación</TabsTrigger>
          <TabsTrigger value="saved">
            Guardados ({savedStyleTransfers.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="latest" className="mt-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Panel de imágenes */}
            <div className="lg:col-span-2 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Imagen original */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <ImageIcon className="w-5 h-5" />
                      Imagen Original
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
                      onClick={() => initImageInputRef.current?.click()}
                    >
                      {initImagePreview ? (
                        <div className="space-y-3">
                          <img
                            src={initImagePreview}
                            alt="Imagen original"
                            className="max-w-full max-h-48 mx-auto rounded-lg object-contain"
                          />
                          <p className="text-sm text-gray-600">
                            {initImage?.name} ({Math.round((initImage?.size || 0) / 1024)} KB)
                          </p>
                          <Button variant="outline" size="sm">
                            Cambiar imagen
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                          <div>
                            <p className="text-lg font-medium text-gray-700">
                              Sube tu imagen original
                            </p>
                            <p className="text-sm text-gray-500">
                              JPEG, PNG o WebP (máx. 10MB)
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <input
                      ref={initImageInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleInitImageSelect}
                      className="hidden"
                    />
                  </CardContent>
                </Card>

                {/* Imagen de estilo */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="w-5 h-5" />
                      Imagen de Estilo
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400 transition-colors"
                      onClick={() => styleImageInputRef.current?.click()}
                    >
                      {styleImagePreview ? (
                        <div className="space-y-3">
                          <img
                            src={styleImagePreview}
                            alt="Imagen de estilo"
                            className="max-w-full max-h-48 mx-auto rounded-lg object-contain"
                          />
                          <p className="text-sm text-gray-600">
                            {styleImage?.name} ({Math.round((styleImage?.size || 0) / 1024)} KB)
                          </p>
                          <Button variant="outline" size="sm">
                            Cambiar imagen
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                          <div>
                            <p className="text-lg font-medium text-gray-700">
                              Sube tu imagen de estilo
                            </p>
                            <p className="text-sm text-gray-500">
                              JPEG, PNG o WebP (máx. 10MB)
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <input
                      ref={styleImageInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleStyleImageSelect}
                      className="hidden"
                    />
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Panel de configuración y resultado */}
            <div className="space-y-6">
              {/* Configuración */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wand2 className="w-5 h-5" />
                    Configuración
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="prompt">Prompt (opcional)</Label>
                    <Textarea
                      id="prompt"
                      placeholder="Ej: Un retrato artístico con colores vibrantes"
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      className="min-h-[80px] mt-2"
                    />
                  </div>

                  <div>
                    <Label htmlFor="negative-prompt">Prompt negativo (opcional)</Label>
                    <Textarea
                      id="negative-prompt"
                      placeholder="Ej: borroso, de baja calidad, distorsionado"
                      value={negativePrompt}
                      onChange={(e) => setNegativePrompt(e.target.value)}
                      className="min-h-[60px] mt-2"
                    />
                  </div>

                  {/* Controles avanzados */}
                  <div className="space-y-4 pt-4 border-t">
                    <div>
                      <Label>Fuerza del estilo: {styleStrength[0].toFixed(2)}</Label>
                      <div className="mt-2 space-y-2">
                        <Slider
                          value={styleStrength}
                          onValueChange={setStyleStrength}
                          min={0}
                          max={1}
                          step={0.05}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>Menos influencia (0.0)</span>
                          <span>Más influencia (1.0)</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label>Fidelidad de composición: {compositionFidelity[0].toFixed(2)}</Label>
                      <div className="mt-2 space-y-2">
                        <Slider
                          value={compositionFidelity}
                          onValueChange={setCompositionFidelity}
                          min={0}
                          max={1}
                          step={0.05}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>Menos fiel (0.0)</span>
                          <span>Más fiel (1.0)</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <Label>Fuerza de cambio: {changeStrength[0].toFixed(2)}</Label>
                      <div className="mt-2 space-y-2">
                        <Slider
                          value={changeStrength}
                          onValueChange={setChangeStrength}
                          min={0.1}
                          max={1}
                          step={0.05}
                          className="w-full"
                        />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>Cambio sutil (0.1)</span>
                          <span>Cambio dramático (1.0)</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Botón de generar */}
                  <Button
                    onClick={handleGenerate}
                    disabled={isGenerating || !initImage || !styleImage}
                    className="w-full h-12 text-lg"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                        Transfiriendo estilo...
                      </>
                    ) : (
                      <>
                        <ArrowRight className="w-5 h-5 mr-2" />
                        Transferir Estilo
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>

              {/* Resultado */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="w-5 h-5" />
                    Resultado
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {generatedImage ? (
                    <div className="space-y-4">
                      <div className="relative group">
                        <img
                          src={generatedImage}
                          alt="Imagen generada"
                          className="w-full rounded-lg shadow-lg"
                        />
                      </div>

                      <div className="flex gap-2 justify-center">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={copyToClipboard}
                        >
                          <Copy className="h-4 w-4 mr-2" />
                          Copiar
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleToggleFavorite}
                          className={currentImageSaved ? "text-red-500 border-red-200 bg-red-50" : ""}
                        >
                          <Heart className={`h-4 w-4 mr-2 ${currentImageSaved ? "fill-current" : ""}`} />
                          {currentImageSaved ? "Guardada" : "Guardar"}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={downloadImage}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Descargar
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="aspect-square bg-gray-50 rounded-lg flex items-center justify-center">
                      <div className="text-center space-y-3">
                        <ArrowRight className="w-16 h-16 text-gray-300 mx-auto" />
                        <p className="text-gray-500">
                          Tu imagen con estilo transferido aparecerá aquí
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="saved" className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5 text-primary" />
                Transferencias de Estilo Guardadas
                <Badge variant="secondary">{savedStyleTransfers.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {savedStyleTransfers.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {savedStyleTransfers.map((savedTransfer) => (
                    <div
                      key={savedTransfer.id}
                      className="bg-white border-2 border-gray-200 rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow"
                    >
                      <div className="relative">
                        <img
                          src={savedTransfer.processedUrl}
                          alt="Imagen guardada"
                          className="w-full h-48 object-cover"
                        />
                        <div className="absolute top-2 right-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const filteredTransfers = savedStyleTransfers.filter(transfer => transfer.id !== savedTransfer.id);
                              setSavedStyleTransfers(filteredTransfers);

                              toast({
                                title: "💔 Eliminada",
                                description: "Imagen eliminada de favoritos.",
                              });
                            }}
                            className="bg-white/90 hover:bg-white"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <div className="p-4">
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          <strong>Prompt:</strong> {savedTransfer.prompt || "Sin prompt"}
                        </p>
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>Fuerza: {savedTransfer.styleStrength}</span>
                          <span>Fidelidad: {savedTransfer.compositionFidelity}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Heart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No hay imágenes guardadas
                  </h3>
                  <p className="text-gray-500">
                    Las imágenes que guardes aparecerán aquí para acceder fácilmente en el futuro.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
