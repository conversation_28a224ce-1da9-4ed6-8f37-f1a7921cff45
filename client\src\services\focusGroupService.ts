import { supabase } from '@/lib/supabase'

/**
 * Interface for Focus Group Simulation data
 */
export interface FocusGroupSimulation {
  id: string
  created_at: string
  updated_at: string
  
  // Usuario propietario
  user_id: string
  
  // Parámetros de entrada
  content: string
  product_category?: string
  context?: string
  custom_questions: string[]
  num_participants: number
  discussion_rounds: number
  
  // Resultados de la simulación
  simulation_results: any
  participants: any[]
  discussions: any[]
  summary: any
  
  // Metadata
  simulation_duration_ms?: number
  status: 'processing' | 'completed' | 'failed'
  error_message?: string
  
  // Gestión de favoritos y organización
  is_favorite: boolean
  custom_name?: string
  tags: string[]
  notes?: string
  
  // Estadísticas
  view_count: number
  last_viewed_at?: string
  regeneration_count: number
}

/**
 * Interface for creating a new focus group simulation
 */
export interface CreateFocusGroupSimulationData {
  content: string
  product_category?: string
  context?: string
  custom_questions?: string[]
  num_participants?: number
  discussion_rounds?: number
  simulation_results: any
  participants: any[]
  discussions: any[]
  summary: any
  simulation_duration_ms?: number
  custom_name?: string
  tags?: string[]
  notes?: string
}

/**
 * Interface for updating a focus group simulation
 */
export interface UpdateFocusGroupSimulationData {
  custom_name?: string
  is_favorite?: boolean
  tags?: string[]
  notes?: string
  view_count?: number
  last_viewed_at?: string
  regeneration_count?: number
}

/**
 * Service for managing focus group simulation data in Supabase
 */
export class FocusGroupService {
  
  /**
   * Save a new focus group simulation to the database
   */
  async saveSimulation(simulationData: CreateFocusGroupSimulationData): Promise<FocusGroupSimulation> {
    const { data, error } = await supabase
      .schema('api')
      .from('focus_group_simulations')
      .insert(simulationData)
      .select()
      .single()

    if (error) {
      console.error('Error saving focus group simulation:', error)
      throw new Error(`Failed to save simulation: ${error.message}`)
    }

    return data
  }

  /**
   * Get all focus group simulations for the current user
   */
  async getUserSimulations(userId: string, options?: {
    limit?: number
    offset?: number
    isFavorite?: boolean
    orderBy?: 'created_at' | 'updated_at'
    orderDirection?: 'asc' | 'desc'
  }): Promise<FocusGroupSimulation[]> {
    let query = supabase
      .schema('api')
      .from('focus_group_simulations')
      .select('*')
      .eq('user_id', userId)

    // Apply filters
    if (options?.isFavorite !== undefined) {
      query = query.eq('is_favorite', options.isFavorite)
    }

    // Apply ordering
    const orderBy = options?.orderBy || 'created_at'
    const ascending = options?.orderDirection === 'asc'
    query = query.order(orderBy, { ascending })

    // Apply pagination
    if (options?.limit) {
      query = query.limit(options.limit)
    }
    if (options?.offset) {
      query = query.range(options.offset, options.offset + (options.limit || 50) - 1)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching focus group simulations:', error)
      throw new Error(`Failed to fetch simulations: ${error.message}`)
    }

    return data || []
  }

  /**
   * Get recent focus group simulations (last 5)
   */
  async getRecentSimulations(userId: string): Promise<FocusGroupSimulation[]> {
    return this.getUserSimulations(userId, {
      limit: 5,
      orderBy: 'created_at',
      orderDirection: 'desc'
    })
  }

  /**
   * Get favorite focus group simulations
   */
  async getFavoriteSimulations(userId: string): Promise<FocusGroupSimulation[]> {
    return this.getUserSimulations(userId, {
      isFavorite: true,
      orderBy: 'updated_at',
      orderDirection: 'desc'
    })
  }

  /**
   * Get a specific focus group simulation by ID
   */
  async getSimulationById(id: string, userId: string): Promise<FocusGroupSimulation | null> {
    const { data, error } = await supabase
      .schema('api')
      .from('focus_group_simulations')
      .select('*')
      .eq('id', id)
      .eq('user_id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Error fetching focus group simulation:', error)
      throw new Error(`Failed to fetch simulation: ${error.message}`)
    }

    return data
  }

  /**
   * Update a focus group simulation
   */
  async updateSimulation(id: string, userId: string, updateData: UpdateFocusGroupSimulationData): Promise<FocusGroupSimulation> {
    const { data, error } = await supabase
      .schema('api')
      .from('focus_group_simulations')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', userId)
      .select()
      .single()

    if (error) {
      console.error('Error updating focus group simulation:', error)
      throw new Error(`Failed to update simulation: ${error.message}`)
    }

    return data
  }

  /**
   * Toggle favorite status of a simulation
   */
  async toggleFavorite(id: string, userId: string): Promise<FocusGroupSimulation> {
    // First get current status
    const current = await this.getSimulationById(id, userId)
    if (!current) {
      throw new Error('Simulation not found')
    }

    return this.updateSimulation(id, userId, {
      is_favorite: !current.is_favorite
    })
  }

  /**
   * Rename a simulation
   */
  async renameSimulation(id: string, userId: string, newName: string): Promise<FocusGroupSimulation> {
    return this.updateSimulation(id, userId, {
      custom_name: newName.trim()
    })
  }

  /**
   * Delete a focus group simulation
   */
  async deleteSimulation(id: string, userId: string): Promise<boolean> {
    const { error } = await supabase
      .schema('api')
      .from('focus_group_simulations')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting focus group simulation:', error)
      throw new Error(`Failed to delete simulation: ${error.message}`)
    }

    return true
  }

  /**
   * Update view count and last viewed timestamp
   */
  async recordView(id: string, userId: string): Promise<void> {
    const current = await this.getSimulationById(id, userId)
    if (!current) return

    await this.updateSimulation(id, userId, {
      view_count: (current.view_count || 0) + 1,
      last_viewed_at: new Date().toISOString()
    })
  }

  /**
   * Increment regeneration count
   */
  async recordRegeneration(id: string, userId: string): Promise<void> {
    const current = await this.getSimulationById(id, userId)
    if (!current) return

    await this.updateSimulation(id, userId, {
      regeneration_count: (current.regeneration_count || 0) + 1
    })
  }

  /**
   * Get simulation statistics for a user
   */
  async getUserStats(userId: string): Promise<{
    totalSimulations: number
    favoriteSimulations: number
    recentActivity: number // simulations in last 7 days
  }> {
    const { data: simulations, error } = await supabase
      .schema('api')
      .from('focus_group_simulations')
      .select('is_favorite, created_at')
      .eq('user_id', userId)

    if (error) {
      console.error('Error fetching user stats:', error)
      throw new Error(`Failed to fetch user stats: ${error.message}`)
    }

    if (!simulations || simulations.length === 0) {
      return {
        totalSimulations: 0,
        favoriteSimulations: 0,
        recentActivity: 0
      }
    }

    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    return {
      totalSimulations: simulations.length,
      favoriteSimulations: simulations.filter(s => s.is_favorite).length,
      recentActivity: simulations.filter(s => 
        new Date(s.created_at) > sevenDaysAgo
      ).length
    }
  }
}

// Export a singleton instance
export const focusGroupService = new FocusGroupService()
