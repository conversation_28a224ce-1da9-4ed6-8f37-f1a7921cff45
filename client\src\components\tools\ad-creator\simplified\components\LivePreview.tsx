/**
 * Live Preview Component
 * Real-time preview of the ad with animations and visual feedback
 */

interface LivePreviewProps {
  headline: string;
  punchline: string;
  cta: string;
  uploadedImage: File | null;
}

export function LivePreview({ headline, punchline, cta, uploadedImage }: LivePreviewProps) {
  return (
    <div className="space-y-6">
      {/* Preview Header */}
      <div className="bg-gradient-to-br from-white to-slate-50 backdrop-blur-xl p-4 rounded-2xl border border-slate-200/60 shadow-lg">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-lg flex items-center justify-center">
            <span className="text-white text-sm font-bold">👁️</span>
          </div>
          <div>
            <h4 className="text-lg font-bold text-slate-900">Live Preview</h4>
            <p className="text-sm text-slate-600">See how your ad will look</p>
          </div>
        </div>
      </div>

      {/* Main Preview */}
      <div className="bg-gradient-to-br from-white to-slate-50 backdrop-blur-xl p-6 rounded-2xl border border-slate-200/60 shadow-lg hover:shadow-xl transition-all duration-300">
        <div className="aspect-square bg-gradient-to-br from-[#3018ef] via-blue-500 to-[#dd3a5a] rounded-2xl p-8 text-white relative overflow-hidden shadow-2xl">
          {/* Animated Background */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-0 right-0 w-24 h-24 bg-white rounded-full blur-2xl animate-pulse delay-1000"></div>
          </div>

          {/* Preview Content */}
          <div className="relative z-10 h-full flex flex-col">
            <div className="text-center mb-6">
              <h3 className={`text-2xl font-bold mb-3 transition-all duration-300 ${
                headline ? 'opacity-100 scale-100' : 'opacity-60 scale-95'
              }`}>
                {headline || "Your main headline here!"}
              </h3>
              <p className={`text-lg leading-relaxed transition-all duration-300 ${
                punchline ? 'opacity-100 scale-100' : 'opacity-60 scale-95'
              }`}>
                {punchline || "Your punchline is here!"}
              </p>
            </div>

            <div className="flex-1 flex items-center justify-center">
              {uploadedImage ? (
                <div className="relative group">
                  <img
                    src={URL.createObjectURL(uploadedImage)}
                    alt="Product preview"
                    className="w-40 h-40 object-cover rounded-2xl shadow-2xl border-4 border-white/30 group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
                </div>
              ) : (
                <div className="w-40 h-40 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border-2 border-dashed border-white/40 hover:border-white/60 transition-colors">
                  <div className="text-center">
                    <span className="text-5xl mb-2 block">📱</span>
                    <p className="text-sm opacity-80">Product Image</p>
                  </div>
                </div>
              )}
            </div>

            {(cta || !cta) && (
              <div className="text-center">
                <button className={`bg-white text-[#3018ef] px-8 py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 ${
                  cta ? 'opacity-100' : 'opacity-60'
                }`}>
                  {cta || "Call to Action"} →
                </button>
              </div>
            )}
          </div>

          {/* Decorative Elements */}
          <div className="absolute top-4 right-4 w-3 h-3 bg-white rounded-full opacity-60 animate-ping"></div>
          <div className="absolute bottom-4 left-4 w-2 h-2 bg-white rounded-full opacity-40 animate-pulse delay-500"></div>
        </div>
      </div>
    </div>
  );
}
