import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, Rocket, ArrowLeft, LogIn } from "lucide-react";
import { <PERSON> } from "wouter";
import { navigation } from "./data";

interface HeaderProps {
  isScrolled: boolean;
}

const Header: React.FC<HeaderProps> = ({ isScrolled }) => {
  return (
    <motion.header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? "py-3 bg-white shadow-lg" : "py-5 bg-transparent"
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/" className="flex items-center mr-6">
            <motion.div
              className="bg-blue-600 w-10 h-10 rounded-full flex items-center justify-center text-white border-3 border-black mr-2"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ArrowLeft size={20} />
            </motion.div>
            <span className="font-bold text-sm hidden sm:block">Volver a Inicio</span>
          </Link>
          <div className="bg-purple-600 w-10 h-10 rounded-full flex items-center justify-center text-white border-3 border-black mr-2">
            <Zap size={20} />
          </div>
          <span className="font-black text-xl">EmmaAI</span>
        </div>
        <div className="hidden md:flex items-center space-x-6">
          {navigation.map((item) => (
            <a 
              key={item.name} 
              href={item.href} 
              className="font-bold hover:text-purple-600 transition-colors"
            >
              {item.name}
            </a>
          ))}
        </div>
        <div className="flex items-center space-x-3">
          <Link href="/login">
            <motion.button
              className="bg-white text-purple-600 font-black py-2 px-4 text-sm rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-gray-50 transition-all duration-300"
              whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
              whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
            >
              <span className="flex items-center justify-center gap-2">
                Iniciar Sesión <LogIn size={16} />
              </span>
            </motion.button>
          </Link>
          <Link href="/login">
            <motion.button
              className="bg-purple-600 text-white font-black py-2 px-4 text-sm rounded-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,0.9)] hover:bg-purple-700 transition-all duration-300"
              whileHover={{ y: -5, boxShadow: "8px 8px 0px 0px rgba(0,0,0,0.9)" }}
              whileTap={{ y: 0, boxShadow: "4px 4px 0px 0px rgba(0,0,0,0.9)" }}
            >
              <span className="flex items-center justify-center gap-2">
                Empezar Gratis <Rocket size={16} />
              </span>
            </motion.button>
          </Link>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
