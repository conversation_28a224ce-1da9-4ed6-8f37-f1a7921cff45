/**
 * SEO & GPT Optimizer™ - PDF Export Utility
 * Utility functions for exporting research to PDF
 */

import jsPDF from 'jspdf';
import { SavedResearch } from '../../../../hooks/seo-gpt-optimizer/useSavedResearch';

export const exportResearchToPDF = (research: SavedResearch): void => {
  try {
    const pdf = new jsPDF();
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    let yPosition = 20;

    // Helper function to add wrapped text
    const addWrappedText = (text: string, x: number, y: number, maxWidth: number): number => {
      const lines = pdf.splitTextToSize(text, maxWidth);
      pdf.text(lines, x, y);
      return lines.length * 5; // Approximate line height
    };

    // Helper function to check page break
    const checkPageBreak = (requiredSpace: number) => {
      if (yPosition + requiredSpace > pageHeight - 20) {
        pdf.addPage();
        yPosition = 20;
      }
    };

    // HEADER
    pdf.setFillColor(48, 24, 239); // #3018ef
    pdf.rect(0, 0, pageWidth, 40, 'F');
    
    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(24);
    pdf.setFont('helvetica', 'bold');
    pdf.text('SEO & GPT Optimizer™', pageWidth / 2, 20, { align: 'center' });
    pdf.setFontSize(14);
    pdf.text('Reporte de Investigación', pageWidth / 2, 30, { align: 'center' });
    
    yPosition = 60;

    // TITLE
    pdf.setTextColor(0, 0, 0);
    pdf.setFontSize(20);
    pdf.setFont('helvetica', 'bold');
    const titleHeight = addWrappedText(research.topic, 20, yPosition, pageWidth - 40);
    yPosition += titleHeight + 15;

    // METADATA
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'normal');
    pdf.text(`Fecha de investigación: ${new Date(research.savedAt).toLocaleDateString('es-ES')}`, 20, yPosition);
    yPosition += 8;
    pdf.text(`Confianza: ${(research.confidence * 100).toFixed(1)}%`, 20, yPosition);
    yPosition += 8;
    pdf.text(`Tiempo de procesamiento: ${research.processingTime.toFixed(1)}s`, 20, yPosition);
    yPosition += 20;

    // CONTENT SECTIONS
    const results = research.results as any;

    // Intent Analysis
    if (results?.intent_analysis) {
      checkPageBreak(30);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(48, 24, 239);
      pdf.text('Análisis de Intención', 20, yPosition);
      yPosition += 12;

      pdf.setFontSize(11);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`Tipo: ${results.intent_analysis.intent_type || 'No disponible'}`, 25, yPosition);
      yPosition += 8;
      const audienceHeight = addWrappedText(`Audiencia: ${results.intent_analysis.target_audience || 'No disponible'}`, 25, yPosition, pageWidth - 50);
      yPosition += audienceHeight + 15;
    }

    // Questions
    if (results?.entities_and_questions?.common_questions?.length > 0) {
      checkPageBreak(30);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(239, 68, 68);
      pdf.text('Preguntas Frecuentes', 20, yPosition);
      yPosition += 12;

      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(0, 0, 0);
      results.entities_and_questions.common_questions.slice(0, 8).forEach((question: string) => {
        checkPageBreak(8);
        const questionHeight = addWrappedText(`• ${question}`, 25, yPosition, pageWidth - 50);
        yPosition += questionHeight + 2;
      });
      yPosition += 10;
    }

    // Content Opportunities
    if (results?.content_opportunities) {
      checkPageBreak(30);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(34, 197, 94);
      pdf.text('Oportunidades de Contenido', 20, yPosition);
      yPosition += 12;

      if (results.content_opportunities.content_gaps?.length > 0) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(0, 0, 0);
        pdf.text('Gaps de Contenido:', 25, yPosition);
        yPosition += 8;

        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');
        results.content_opportunities.content_gaps.slice(0, 5).forEach((gap: string) => {
          checkPageBreak(8);
          const gapHeight = addWrappedText(`• ${gap}`, 30, yPosition, pageWidth - 60);
          yPosition += gapHeight + 2;
        });
        yPosition += 8;
      }

      if (results.content_opportunities.target_keywords?.length > 0) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Palabras Clave:', 25, yPosition);
        yPosition += 8;

        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');
        const keywordsText = results.content_opportunities.target_keywords.slice(0, 10).join(', ');
        const keywordsHeight = addWrappedText(keywordsText, 30, yPosition, pageWidth - 60);
        yPosition += keywordsHeight + 15;
      }
    }

    // Google Results
    if (results?.google_results?.results?.length > 0) {
      checkPageBreak(30);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(59, 130, 246);
      pdf.text('Resultados de Google', 20, yPosition);
      yPosition += 12;

      pdf.setFontSize(11);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`Total de resultados: ${results.google_results.total_results?.toLocaleString() || 0}`, 25, yPosition);
      yPosition += 10;

      results.google_results.results.slice(0, 3).forEach((result: any, index: number) => {
        checkPageBreak(15);
        pdf.setFont('helvetica', 'bold');
        pdf.text(`${index + 1}. ${result.title}`, 25, yPosition);
        yPosition += 6;

        pdf.setFont('helvetica', 'normal');
        pdf.setTextColor(34, 197, 94);
        pdf.text(result.domain, 30, yPosition);
        yPosition += 6;

        pdf.setTextColor(0, 0, 0);
        const snippetHeight = addWrappedText(result.snippet, 30, yPosition, pageWidth - 60);
        yPosition += snippetHeight + 8;
      });
      yPosition += 10;
    }

    // Social Insights
    if (results?.social_insights) {
      checkPageBreak(30);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(255, 165, 0);
      pdf.text('Insights Sociales', 20, yPosition);
      yPosition += 12;

      // Reddit Insights
      if (results.social_insights.reddit?.insights?.length > 0) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(0, 0, 0);
        pdf.text('Reddit:', 25, yPosition);
        yPosition += 8;

        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');
        results.social_insights.reddit.insights.slice(0, 3).forEach((insight: any) => {
          checkPageBreak(12);
          const titleHeight = addWrappedText(`• ${insight.title}`, 30, yPosition, pageWidth - 60);
          yPosition += titleHeight + 2;
          const snippetHeight = addWrappedText(`  ${insight.snippet}`, 35, yPosition, pageWidth - 70);
          yPosition += snippetHeight + 3;
        });
        yPosition += 5;
      }

      // Quora Insights
      if (results.social_insights.quora?.insights?.length > 0) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(0, 0, 0);
        pdf.text('Quora:', 25, yPosition);
        yPosition += 8;

        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');
        results.social_insights.quora.insights.slice(0, 3).forEach((insight: any) => {
          checkPageBreak(12);
          const questionHeight = addWrappedText(`• ${insight.question}`, 30, yPosition, pageWidth - 60);
          yPosition += questionHeight + 2;
          const answerHeight = addWrappedText(`  ${insight.answer_preview}`, 35, yPosition, pageWidth - 70);
          yPosition += answerHeight + 3;
        });
        yPosition += 10;
      }
    }

    // GPT Reference
    if (results?.gpt_reference) {
      checkPageBreak(30);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(128, 0, 128);
      pdf.text('Referencia GPT', 20, yPosition);
      yPosition += 12;

      pdf.setFontSize(11);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(0, 0, 0);
      const responseHeight = addWrappedText(results.gpt_reference.response, 25, yPosition, pageWidth - 50);
      yPosition += responseHeight + 15;
    }

    // Research Summary
    if (results?.research_summary) {
      checkPageBreak(30);
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(16, 185, 129);
      pdf.text('Resumen de Investigación', 20, yPosition);
      yPosition += 12;

      if (results.research_summary.key_findings?.length > 0) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(0, 0, 0);
        pdf.text('Hallazgos Clave:', 25, yPosition);
        yPosition += 8;

        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');
        results.research_summary.key_findings.forEach((finding: string) => {
          checkPageBreak(8);
          const findingHeight = addWrappedText(`• ${finding}`, 30, yPosition, pageWidth - 60);
          yPosition += findingHeight + 2;
        });
        yPosition += 8;
      }

      if (results.research_summary.recommendations?.length > 0) {
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text('Recomendaciones:', 25, yPosition);
        yPosition += 8;

        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');
        results.research_summary.recommendations.forEach((rec: string) => {
          checkPageBreak(8);
          const recHeight = addWrappedText(`• ${rec}`, 30, yPosition, pageWidth - 60);
          yPosition += recHeight + 2;
        });
        yPosition += 15;
      }
    }

    // FOOTER
    checkPageBreak(20);
    yPosition = pageHeight - 30;
    pdf.setFillColor(30, 41, 59);
    pdf.rect(0, yPosition - 5, pageWidth, 25, 'F');

    pdf.setTextColor(255, 255, 255);
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');
    pdf.text('Generado por SEO & GPT Optimizer - Emma Studio', pageWidth / 2, yPosition + 5, { align: 'center' });
    pdf.text('Este reporte contiene análisis basado en IA y datos de múltiples fuentes', pageWidth / 2, yPosition + 12, { align: 'center' });

    // Download PDF
    const fileName = `investigacion-${research.topic.replace(/[^a-zA-Z0-9]/g, '-')}-${Date.now()}.pdf`;
    pdf.save(fileName);

  } catch (error) {
    console.error('Error al exportar PDF:', error);
    // Fallback: download as JSON
    const dataStr = JSON.stringify(research.results, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `research-${research.topic}-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  }
};
