/**
 * Size Selection Step Component
 * First step of the simplified ad creator - size selection with premium features showcase
 */

import { Sparkles, Download, Heart } from "lucide-react";
import { PlatformConfig } from "@/types/ad-creator-types";
import { SizeOption } from "../types/simplified-ad-types";
import { SizeSelector } from "../components/SizeSelector";

interface SizeSelectionStepProps {
  platform: string;
  config: PlatformConfig;
  onSizeSelected: (size: SizeOption) => void;
}

export function SizeSelectionStep({ platform, config, onSizeSelected }: SizeSelectionStepProps) {
  return (
    <div className="space-y-12">
      {/* Size Selector */}
      <SizeSelector
        platform={platform}
        config={config}
        onSizeSelected={onSizeSelected}
      />

      {/* Premium features showcase */}
      <div className="max-w-4xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 bg-white/50 backdrop-blur-sm rounded-xl border border-white/20">
            <div className="w-12 h-12 bg-gradient-to-r from-[#3018ef] to-[#4c51bf] rounded-xl flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <h4 className="font-semibold text-slate-900 mb-2">IA Avanzada</h4>
            <p className="text-sm text-slate-600">Generación con modelos de última generación</p>
          </div>
          <div className="text-center p-6 bg-white/50 backdrop-blur-sm rounded-xl border border-white/20">
            <div className="w-12 h-12 bg-gradient-to-r from-[#dd3a5a] to-[#e55a7a] rounded-xl flex items-center justify-center mx-auto mb-4">
              <Download className="w-6 h-6 text-white" />
            </div>
            <h4 className="font-semibold text-slate-900 mb-2">Alta Resolución</h4>
            <p className="text-sm text-slate-600">Imágenes optimizadas para cada plataforma</p>
          </div>
          <div className="text-center p-6 bg-white/50 backdrop-blur-sm rounded-xl border border-white/20">
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-4">
              <Heart className="w-6 h-6 text-white" />
            </div>
            <h4 className="font-semibold text-slate-900 mb-2">Múltiples Variaciones</h4>
            <p className="text-sm text-slate-600">6 opciones únicas por generación</p>
          </div>
        </div>
      </div>
    </div>
  );
}
