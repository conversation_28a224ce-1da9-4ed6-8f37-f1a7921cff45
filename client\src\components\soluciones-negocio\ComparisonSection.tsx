import React from "react";
import { motion } from "framer-motion";
import { comparisonData } from "./data";
import { CheckCircle, XCircle } from "lucide-react";

const ComparisonSection: React.FC = () => {
  return (
    <section id="comparativa" className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50">
      <div className="container mx-auto">
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-black mb-4 text-gray-900">
              Por Qué <span className="text-purple-600">Emma IA</span> Supera al Marketing Tradicional
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Descubre cómo nuestros agentes IA transforman completamente la forma en que se hace marketing, 
              ofreciendo resultados superiores con menos recursos.
            </p>
          </motion.div>
        </div>

        <div className="space-y-16">
          {comparisonData.map((comparison, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                <div className="bg-white p-8 rounded-2xl shadow-lg border-2 border-gray-100">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6">{comparison.traditional}</h3>
                  <div className="space-y-4">
                    {comparison.differences.map((diff, idx) => (
                      <div key={idx} className="flex items-start">
                        <XCircle className="w-6 h-6 text-red-500 mr-3 flex-shrink-0" />
                        <p className="text-gray-700">{diff.trad}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="hidden lg:flex justify-center">
                  <div className="relative">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-full border-t-2 border-dashed border-gray-300"></div>
                    </div>
                    <div className="relative flex justify-center">
                      <span className="bg-gray-50 px-4 text-lg font-bold text-gray-500">VS</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-purple-600 to-indigo-600 p-8 rounded-2xl shadow-lg text-white">
                  <h3 className="text-2xl font-bold mb-6">{comparison.emma}</h3>
                  <div className="space-y-4">
                    {comparison.differences.map((diff, idx) => (
                      <div key={idx} className="flex items-start">
                        <CheckCircle className="w-6 h-6 text-green-400 mr-3 flex-shrink-0" />
                        <p className="text-white">{diff.emma}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="mt-20 bg-white p-8 rounded-2xl shadow-lg border-2 border-gray-100"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="text-4xl font-black text-purple-600 mb-2">150%</div>
              <p className="text-gray-700">Aumento promedio en ROI</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-black text-purple-600 mb-2">85%</div>
              <p className="text-gray-700">Reducción en tiempo de ejecución</p>
            </div>
            <div className="text-center">
              <div className="text-4xl font-black text-purple-600 mb-2">67%</div>
              <p className="text-gray-700">Reducción en costos operativos</p>
            </div>
          </div>
        </motion.div>
        
        <motion.div
          className="mt-12 mb-8"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          {/* Elementos decorativos */}
          <motion.div 
            className="absolute -left-10 w-20 h-20 rounded-full bg-yellow-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hidden lg:block"
            animate={{ 
              y: [0, -15, 0],
              rotate: [0, 10, 0]
            }}
            transition={{ 
              duration: 5,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          
          <motion.div 
            className="absolute -right-10 w-20 h-20 rounded-full bg-pink-300 border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.9)] hidden lg:block"
            animate={{ 
              y: [0, 15, 0],
              rotate: [0, -8, 0]
            }}
            transition={{ 
              duration: 4,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 1
            }}
          />
          
          <h2 className="text-6xl sm:text-7xl font-black text-center">
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-transparent bg-clip-text drop-shadow-[4px_4px_0px_rgba(0,0,0,0.3)]">
              SI GASTAS MENOS,
            </span>
            <br />
            <span className="bg-gradient-to-r from-pink-600 via-purple-600 to-blue-600 text-transparent bg-clip-text drop-shadow-[4px_4px_0px_rgba(0,0,0,0.3)]">
              GANAS MÁS
            </span>
          </h2>
        </motion.div>
      </div>
    </section>
  );
};

export default ComparisonSection;
