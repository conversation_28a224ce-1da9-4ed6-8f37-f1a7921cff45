/**
 * AdResultsActions - Componente para las acciones después de generar anuncios
 */

import React from 'react';
import { motion } from 'framer-motion';
import { RefreshCw, Download, Share2, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AdResultsActionsProps {
  generatedAdsCount: number;
  onGenerateMore: () => void;
  onDownloadAll?: () => void;
  onShareAll?: () => void;
  onBackToEdit: () => void;
  isGenerating?: boolean;
}

export function AdResultsActions({
  generatedAdsCount,
  onGenerateMore,
  onDownloadAll,
  onShareAll,
  onBackToEdit,
  isGenerating = false
}: AdResultsActionsProps) {
  if (generatedAdsCount === 0) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.6 }}
      className="text-center space-y-6"
    >
      <div className="max-w-2xl mx-auto p-8 bg-white/70 backdrop-blur-xl rounded-2xl border border-white/20">
        <h3 className="text-xl font-bold text-slate-900 mb-4">
          ¿Te gustaron los resultados?
        </h3>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={onBackToEdit}
            variant="outline"
            className="border-[#3018ef]/20 hover:bg-[#3018ef]/5"
            disabled={isGenerating}
          >
            <Settings className="w-4 h-4 mr-2" />
            Editar y Regenerar
          </Button>
          
          <Button
            onClick={onGenerateMore}
            className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] hover:from-[#3018ef]/90 hover:to-[#dd3a5a]/90"
            disabled={isGenerating}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Generar 6 Más
          </Button>
        </div>

        {/* Additional actions */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center mt-4 pt-4 border-t border-slate-200">
          {onDownloadAll && (
            <Button
              onClick={onDownloadAll}
              variant="ghost"
              size="sm"
              className="text-slate-600 hover:text-slate-900"
            >
              <Download className="w-4 h-4 mr-2" />
              Descargar Todos
            </Button>
          )}
          
          {onShareAll && (
            <Button
              onClick={onShareAll}
              variant="ghost"
              size="sm"
              className="text-slate-600 hover:text-slate-900"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Compartir Colección
            </Button>
          )}
        </div>

        <p className="text-sm text-slate-500 mt-4">
          {generatedAdsCount} anuncios generados con IA avanzada
        </p>
      </div>
    </motion.div>
  );
}

export default AdResultsActions;
