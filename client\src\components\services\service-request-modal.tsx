import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Loader2 } from "lucide-react";

import type { ServiceProvider } from "@shared/schema";

// Definición del esquema del formulario
const requestFormSchema = z.object({
  title: z.string().min(5, "El título debe tener al menos 5 caracteres"),
  description: z
    .string()
    .min(20, "La descripción debe tener al menos 20 caracteres"),
  budget: z.string().optional(),
  isAiIntegrationRequired: z.boolean().default(false),
});

type RequestFormValues = z.infer<typeof requestFormSchema>;

// Props del componente
interface ServiceRequestModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  provider: ServiceProvider;
}

export function ServiceRequestModal({
  open,
  onOpenChange,
  provider,
}: ServiceRequestModalProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mutación para enviar solicitudes de servicio
  const createRequestMutation = useMutation({
    mutationFn: (data: any) => {
      return apiRequest({
        url: "/api/service-requests",
        method: "POST",
        data,
      });
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: ["/api/service-requests"] });

      // Mostrar notificación de éxito
      toast({
        title: "Solicitud enviada",
        description: "Tu solicitud ha sido enviada correctamente",
      });

      // Cerrar modal y reiniciar formulario
      onOpenChange(false);
      form.reset();
    },
    onError: (error, variables, context) => {
      console.error("Error al enviar la solicitud:", error);
      toast({
        title: "Error",
        description: "Ha ocurrido un error al enviar la solicitud",
        variant: "destructive",
      });
    },
  });

  // Inicialización del formulario
  const form = useForm<RequestFormValues>({
    resolver: zodResolver(requestFormSchema),
    defaultValues: {
      title: "",
      description: "",
      budget: "",
      isAiIntegrationRequired: false,
    },
  });

  // Función para manejar el envío del formulario
  const handleSubmit = (values: RequestFormValues) => {
    if (!user) {
      toast({
        title: "Error",
        description: "Debes iniciar sesión para solicitar servicios",
        variant: "destructive",
      });
      return;
    }

    // Convertir presupuesto a número si existe
    const budgetValue = values.budget ? parseFloat(values.budget) : null;

    // Datos de la solicitud
    const requestData = {
      userId: user.id,
      providerId: provider.id,
      title: values.title,
      description: values.description,
      budget: budgetValue,
      isAiIntegrationRequired: values.isAiIntegrationRequired,
    };

    // Utilizar la mutación para enviar la solicitud
    setIsSubmitting(true);
    createRequestMutation.mutate(requestData, {
      onSettled: () => {
        setIsSubmitting(false);
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Solicitud de servicio</DialogTitle>
          <DialogDescription>
            Completa los detalles para enviar una solicitud a {provider.title}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <div className="flex items-center gap-4 p-3 bg-blue-50 rounded-lg">
              <img
                src={
                  provider.imageUrl ||
                  "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3"
                }
                alt={provider.title}
                className="w-12 h-12 rounded-full object-cover"
              />
              <div>
                <h4 className="font-medium">{provider.title}</h4>
                <p className="text-sm text-gray-600">{provider.specialty}</p>
              </div>
            </div>

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Título de la solicitud</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Ej: Diseño de logotipo para mi empresa"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Un título claro que describa lo que necesitas
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descripción detallada</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe en detalle qué necesitas, incluyendo objetivos, requisitos específicos y cualquier otra información relevante..."
                      className="min-h-[120px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Sé específico para recibir la mejor propuesta posible
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="budget"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Presupuesto (opcional)</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2">
                          €
                        </span>
                        <Input
                          type="number"
                          className="pl-8"
                          placeholder="0.00"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Indica tu presupuesto aproximado
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isAiIntegrationRequired"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Integración con IA</FormLabel>
                    <FormDescription className="mt-0">
                      ¿Requiere integración con agentes de IA?
                    </FormDescription>
                    <FormControl className="mt-auto">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          id="ai-integration"
                        />
                        <label
                          htmlFor="ai-integration"
                          className="text-sm font-medium leading-none cursor-pointer"
                        >
                          {field.value ? "Sí" : "No"}
                        </label>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            <DialogFooter>
              <Button
                variant="outline"
                type="button"
                onClick={() => onOpenChange(false)}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Enviando...
                  </>
                ) : (
                  "Enviar solicitud"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
