import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { User } from "lucide-react";

const testMessages = [
  {
    id: "1",
    sender: "user",
    message: "<PERSON><PERSON>, me interesa su producto",
    timestamp: new Date().toISOString()
  },
  {
    id: "2", 
    sender: "persona",
    message: "Este es un mensaje extremadamente largo que debería probar el word-wrap y asegurar que el texto se ajuste correctamente dentro de la caja de mensaje sin salirse de los bordes. Incluye palabras muy largas como supercalifragilisticoespialidoso y URLs como https://www.ejemplo-de-url-muy-larga-que-podria-causar-problemas-de-overflow.com/path/to/resource?param1=value1&param2=value2&param3=value3",
    timestamp: new Date().toISOString()
  },
  {
    id: "3",
    sender: "user", 
    message: "¿Cómo funciona exactamente? Necesito más información sobre las características técnicas y los beneficios que ofrece su plataforma para empresas como la nuestra que manejan múltiples proyectos simultáneamente",
    timestamp: new Date().toISOString()
  },
  {
    id: "4",
    sender: "persona",
    message: "Palabrasmuylargassinespacionicaracteresespecialesquepodrianromperlalayoutdelchatsinosetienelaconfiguracioncorrectadewordwrapyoverflowwrap",
    timestamp: new Date().toISOString()
  }
];

export function ChatMessageTest() {
  return (
    <div className="p-8 max-w-4xl mx-auto">
      <Card className="h-[600px] flex flex-col overflow-hidden">
        <CardHeader className="border-b">
          <CardTitle>Test de Mensajes de Chat - Word Wrap</CardTitle>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col p-0 overflow-hidden">
          <div className="flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4">
            {testMessages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === "user" ? "justify-end" : "justify-start"}`}
              >
                <div className={`flex items-start gap-3 max-w-[80%] ${
                  message.sender === "user" ? "flex-row-reverse" : "flex-row"
                }`}>
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    {message.sender === "user" ? (
                      <AvatarFallback>
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    ) : (
                      <AvatarFallback>P</AvatarFallback>
                    )}
                  </Avatar>
                  <div className={`rounded-lg p-3 min-w-0 flex-1 max-w-full ${
                    message.sender === "user"
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 text-gray-900"
                  }`}>
                    <p className="text-sm chat-message-text force-text-wrap">
                      {message.message}
                    </p>
                    <p className={`text-xs mt-1 ${
                      message.sender === "user" ? "text-blue-100" : "text-gray-500"
                    }`}>
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
