/**
 * SEO & GPT Optimizer™ - Data Completeness Hook
 * Hook for checking research data completeness
 */

import { useMemo } from 'react';
import { SavedResearch } from '../../../../hooks/seo-gpt-optimizer/useSavedResearch';

interface DataCompletenessResult {
  sections: {
    intent_analysis: boolean;
    google_results: boolean;
    common_questions: boolean;
    content_opportunities: boolean;
    social_insights: boolean;
    gpt_reference: boolean;
    research_summary: boolean;
    research_quality_metrics: boolean;
  };
  completeness: number;
  isComplete: boolean;
}

export const useDataCompleteness = (research: SavedResearch): DataCompletenessResult => {
  return useMemo(() => {
    const results = research.results as any;
    
    const sections = {
      intent_analysis: !!results?.intent_analysis,
      google_results: !!results?.google_results?.results?.length,
      common_questions: !!results?.entities_and_questions?.common_questions?.length,
      content_opportunities: !!results?.content_opportunities,
      social_insights: !!(results?.social_insights?.reddit || results?.social_insights?.quora),
      gpt_reference: !!results?.gpt_reference,
      research_summary: !!results?.research_summary,
      research_quality_metrics: !!results?.research_quality_metrics
    };

    const completeSections = Object.values(sections).filter(Boolean).length;
    const totalSections = Object.keys(sections).length;

    return {
      sections,
      completeness: (completeSections / totalSections) * 100,
      isComplete: completeSections >= 5 // Al menos 5 de 8 secciones
    };
  }, [research]);
};
