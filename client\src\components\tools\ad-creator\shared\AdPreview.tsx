/**
 * Shared Ad Preview Component
 * Reusable preview for all platform editors
 */

import { motion } from "framer-motion";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Download, 
  Heart, 
  Trash2, 
  Loader2,
  Image as ImageIcon,
  Sparkles
} from "lucide-react";
import { AdPreviewProps } from "@/types/ad-creator-types";

export function AdPreview({
  currentAd,
  isGenerating,
  mainTab,
  savedAds,
  productImages,
  useProductImages,
  onTabChange,
  onSaveAd,
  onDownload,
  onRemoveAd,
  getStats,
  config
}: AdPreviewProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-4"
    >
      {/* Preview principal */}
      <Card className="overflow-hidden">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <config.icon className="h-5 w-5" />
            Vista Previa - {config.name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
            {isGenerating ? (
              <div className="text-center space-y-4">
                <Loader2 className="h-12 w-12 animate-spin text-[#3018ef] mx-auto" />
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-700">Generando anuncio...</p>
                  <p className="text-xs text-gray-500">Esto puede tomar unos segundos</p>
                </div>
              </div>
            ) : currentAd ? (
              <div className="relative w-full h-full">
                <img
                  src={currentAd.image_url}
                  alt="Anuncio generado"
                  className="w-full h-full object-cover rounded"
                />
                <div className="absolute top-2 right-2 flex gap-2">
                  <Button
                    onClick={() => onSaveAd(currentAd)}
                    size="sm"
                    className="bg-white/90 hover:bg-white text-gray-700 shadow-sm"
                  >
                    <Heart className="w-4 h-4" />
                  </Button>
                  <Button
                    onClick={() => onDownload(currentAd.image_url)}
                    size="sm"
                    className="bg-white/90 hover:bg-white text-gray-700 shadow-sm"
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center space-y-4">
                <ImageIcon className="h-16 w-16 text-gray-400 mx-auto" />
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-600">
                    Tu anuncio aparecerá aquí
                  </p>
                  <p className="text-xs text-gray-500">
                    Describe tu anuncio y haz clic en "Generar"
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Información del anuncio actual */}
          {currentAd && (
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Prompt usado:</span>
                <Badge variant="outline" className="text-xs">
                  {config.name}
                </Badge>
              </div>
              <p className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                {currentAd.prompt}
              </p>
              {currentAd.metadata?.usedProductImages && (
                <div className="flex items-center gap-2">
                  <Sparkles className="w-4 h-4 text-[#3018ef]" />
                  <span className="text-xs text-[#3018ef] font-medium">
                    Generado con {currentAd.metadata.productImageCount} imagen(es) de producto
                  </span>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tabs para anuncios */}
      <Card>
        <CardContent className="p-0">
          <Tabs value={mainTab} onValueChange={onTabChange}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="latest">Último Generado</TabsTrigger>
              <TabsTrigger value="saved">
                Guardados ({getStats().total})
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="latest" className="p-4">
              {currentAd ? (
                <div className="space-y-3">
                  <div className="text-center">
                    <p className="text-sm font-medium text-gray-700">
                      Anuncio generado exitosamente
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(currentAd.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-sm text-gray-600">
                    Aún no has generado ningún anuncio
                  </p>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="saved" className="p-4">
              {savedAds.length > 0 ? (
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {savedAds.slice(0, 5).map((ad) => (
                    <div key={ad.id} className="flex items-center gap-3 p-2 border rounded">
                      <img
                        src={ad.image_url}
                        alt="Anuncio guardado"
                        className="w-12 h-12 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-xs font-medium truncate">
                          {ad.prompt.substring(0, 50)}...
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(ad.timestamp).toLocaleDateString()}
                        </p>
                      </div>
                      <Button
                        onClick={() => onRemoveAd(ad.id)}
                        size="sm"
                        variant="ghost"
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-sm text-gray-600">
                    No tienes anuncios guardados
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Imágenes de producto preview */}
      {useProductImages && productImages.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Imágenes de Producto</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-2">
              {productImages.map((file, index) => (
                <img
                  key={index}
                  src={URL.createObjectURL(file)}
                  alt={`Producto ${index + 1}`}
                  className="w-full h-16 object-cover rounded border"
                />
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </motion.div>
  );
}
